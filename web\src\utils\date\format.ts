import DateTime from './date-time'

/**
 * 格式化精确时间
 * @param timestamp 时间戳（毫秒）
 * @returns 格式化后的精确时间字符串
 */
export function formatExactTime(timestamp: number): string {
  return DateTime.toTimeString(timestamp.toString(), 'YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化相对时间
 * @param timestamp 时间戳（毫秒）
 * @returns 相对时间字符串（如：剩余3天、已过期等）
 */
export function formatRelativeTime(timestamp: number): string {
  const now = Date.now()
  const diff = timestamp - now

  // 已过期
  if (diff <= 0) {
    return '已过期'
  }

  // 不到1分钟
  if (diff < 60 * 1000) {
    return '即将过期'
  }

  // 分钟级别
  if (diff < 60 * 60 * 1000) {
    return `剩余${Math.floor(diff / (60 * 1000))}分钟`
  }

  // 小时级别
  if (diff < 24 * 60 * 60 * 1000) {
    return `剩余${Math.floor(diff / (60 * 60 * 1000))}小时`
  }

  // 天级别
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    return `剩余${Math.floor(diff / (24 * 60 * 60 * 1000))}天`
  }

  // 月级别
  if (diff < 12 * 30 * 24 * 60 * 60 * 1000) {
    const months = Math.floor(diff / (30 * 24 * 60 * 60 * 1000))
    return `剩余${months}个月`
  }

  // 年级别
  const years = Math.floor(diff / (12 * 30 * 24 * 60 * 60 * 1000))
  return `剩余${years}年`
}
