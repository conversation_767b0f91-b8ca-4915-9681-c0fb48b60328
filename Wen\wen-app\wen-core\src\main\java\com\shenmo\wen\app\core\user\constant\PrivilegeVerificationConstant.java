package com.shenmo.wen.app.core.user.constant;

/**
 * 特权验证常量
 * 
 * <AUTHOR>
 */
public interface PrivilegeVerificationConstant {

    /**
     * Redis Key前缀
     */
    String REDIS_KEY_PREFIX = "wen:user:privilege:";

    /**
     * 短信验证页面模板名称
     */
    String SMS_VERIFICATION_TEMPLATE = "sms_verification";

    /**
     * 1
     * 二维码验证页面模板名称
     */
    String QR_CODE_VERIFICATION_TEMPLATE = "qr_code_verification";

    /**
     * 邮件主题模板
     */
    String EMAIL_SUBJECT_TEMPLATE = "[%s] 确认验证特权 - %s";

    /**
     * 短信验证邮件内容模板
     */
    String SMS_EMAIL_CONTENT_TEMPLATE = "是否提供给 [%s] 对应的 [%s] 短信验证码等安全警示信息，如果同意请点击下方链接后等待短信验证码并输入";

    /**
     * 二维码验证邮件内容模板
     */
    String QR_CODE_EMAIL_CONTENT_TEMPLATE = "是否同意在 [%s] 对应的 [%s] 登录二维码进行扫描登录等安全警示信息，如果同意点开二维码链接并扫描";

    /**
     * 验证码长度
     */
    int VERIFICATION_CODE_LENGTH = 6;
}
