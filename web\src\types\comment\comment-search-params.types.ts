/**
 * 评论搜索请求参数类型定义
 */

import type { RequestParams } from '@/types/api/request.types'

/**
 * 评论搜索请求参数类型
 */
export interface CommentSearchParams extends RequestParams {
  /** 搜索关键词 */
  searchKey?: string
  /** 是否只搜索自己的内容 */
  owner?: boolean
  /** 是否包含互动内容 */
  interaction?: boolean
  /** 是否只搜索收藏的内容 */
  favorite?: boolean
  /** 标签筛选条件 */
  tag?: string
  /** 评论ID（用于游标分页） */
  id?: string | number
  /** 加载数量限制 */
  loadSize?: number
  /** 其他扩展参数 */
  [key: string]: string | number | boolean | null | undefined
}
