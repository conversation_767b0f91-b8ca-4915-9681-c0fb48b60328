<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码验证 - {subjectPrefix}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            line-height: 1.6;
            color: #1a202c;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 500px;
            width: 100%;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        h1 {
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 1;
        }
        .content {
            padding: 40px 30px;
        }
        .warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            color: #92400e;
            font-weight: 500;
        }
        .info {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #bae6fd;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            color: #0c4a6e;
        }
        .qr-container {
            text-align: center;
            margin: 32px 0;
            padding: 24px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 16px;
            border: 2px solid #e2e8f0;
        }
        .qr-code {
            border: 3px solid #4f46e5;
            border-radius: 16px;
            padding: 16px;
            background: white;
            display: inline-block;
            box-shadow: 0 8px 20px rgba(79, 70, 229, 0.2);
        }
        .qr-placeholder {
            width: 200px;
            height: 200px;
            border: 3px dashed #cbd5e0;
            border-radius: 16px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            font-weight: 500;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        .status {
            text-align: center;
            margin: 24px 0;
            font-size: 18px;
            font-weight: 600;
            padding: 16px;
            border-radius: 12px;
        }
        .status.waiting {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        .status.success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid #10b981;
        }
        .form-group {
            margin-bottom: 24px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
            font-size: 16px;
        }
        input[type="text"] {
            width: 100%;
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8fafc;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #4f46e5;
            background: #ffffff;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        .btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            margin-right: 12px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
        }
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(107, 114, 128, 0.4);
        }
        .text-center {
            text-align: center;
        }
        .icon {
            font-size: 18px;
            margin-right: 8px;
        }
        .verification-instructions {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #bae6fd;
            border-radius: 12px;
            padding: 20px;
            margin: 24px 0;
        }
        .verification-instructions h3 {
            margin: 0 0 12px 0;
            color: #0c4a6e;
        }
        .verification-instructions ol {
            margin: 0;
            padding-left: 20px;
            color: #0c4a6e;
        }
        .verification-instructions li {
            margin-bottom: 8px;
        }
        .auto-tip {
            margin-top: 12px;
            font-size: 14px;
            color: #64748b;
            font-style: italic;
        }
        .action-buttons {
            text-align: center;
            margin: 32px 0;
        }
        .action-buttons .btn {
            margin: 0 8px;
            min-width: 160px;
        }
        .success-message {
            animation: slideIn 0.5s ease-out;
        }
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        @media (max-width: 640px) {
            body {
                padding: 10px;
            }
            .content {
                padding: 30px 20px;
            }
            .header {
                padding: 30px 20px;
            }
            .btn {
                width: 100%;
                margin-right: 0;
                margin-bottom: 12px;
            }
            .qr-placeholder {
                width: 160px;
                height: 160px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 二维码验证</h1>
        </div>

        <div class="content">
            <div class="warning">
                <span class="icon">⚠️</span>
                <strong>重要提醒</strong><br>
                请确保您有权限验证此用户的身份信息。未经授权的验证可能涉及隐私泄露。
            </div>

            <div class="info">
                <p><span class="icon">👤</span><strong>验证信息</strong></p>
                <p><strong>申请用户：</strong>{userAUsername}</p>
                <p><strong>特权类型：</strong>{templateName}</p>
            </div>

            <div class="qr-container">
                <div class="qr-code-display" id="qrCodeDisplay">
                    <img src="{qrCodeBase64}" alt="二维码" style="width: 200px; height: 200px; border-radius: 12px; box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);" />
                </div>
                <p style="margin-top: 16px; color: #64748b; font-weight: 500;">
                    📱 请使用手机扫描上方二维码进行验证
                </p>
                <p style="margin-top: 8px; color: #94a3b8; font-size: 14px;">
                    💡 此二维码根据用户上传的内容生成
                </p>
                <div style="margin-top: 12px; font-size: 12px; color: #64748b; word-break: break-all; max-width: 200px; margin-left: auto; margin-right: auto;">
                    内容: {qrCodeContent}
                </div>
            </div>

            <div class="verification-instructions">
                <h3>📋 验证步骤：</h3>
                <ol>
                    <li>使用手机扫描上方二维码</li>
                    <li>在手机上完成相应的验证操作</li>
                    <li>完成后请点击下方按钮确认</li>
                </ol>
                <p class="auto-tip">💡 如不手动确认，系统将在适当时间后自动完成验证</p>
            </div>

            <div class="action-buttons">
                <button type="button" class="btn btn-primary" id="manualConfirmBtn">
                    ✅ 我已完成验证
                </button>
                <button type="button" class="btn btn-secondary" onclick="window.close()">
                    ❌ 取消验证
                </button>
            </div>
        </div>
    </div>



    <script>
        let timeCheckInterval = null;
        let verificationCompleted = false;
        let timerStarted = false;

        // 二维码内容
        const qrCodeContent = '{qrCodeContent}';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeVerification();
        });

        async function initializeVerification() {
            // 启动计时器
            await startTimer();

            // 开始监控状态
            startStatusMonitoring();

            // 绑定手动确认按钮
            document.getElementById('manualConfirmBtn').addEventListener('click', manualConfirm);
        }

        async function startTimer() {
            try {
                const response = await fetch(`/core/user-privilege-verification/{verificationId}/start-timer`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success && result.data) {
                    timerStarted = true;
                    console.log('计时器已启动，剩余时间:', result.data.remainingSeconds, '秒');

                    // 如果已经自动完成了
                    if (result.data.autoCompleted) {
                        await handleAutoCompletion();
                    }
                } else {
                    console.error('启动计时器失败:', result.message);
                }
            } catch (error) {
                console.error('启动计时器请求失败:', error);
            }
        }

        async function checkTimeStatus() {
            try {
                const response = await fetch(`/core/user-privilege-verification/{verificationId}/remaining-time`);
                const result = await response.json();

                if (result.success && result.data) {
                    const timeInfo = result.data;

                    if (timeInfo.autoCompleted) {
                        // 已自动完成
                        await handleAutoCompletion();
                        return false; // 停止检查
                    } else if (timeInfo.remainingSeconds <= 0) {
                        // 时间到了，等待后端自动完成
                        console.log('时间已到，等待自动完成...');
                        return true; // 继续检查，等待后端处理
                    }

                    // 可以在这里添加调试信息（不显示给用户）
                    console.log('剩余时间:', timeInfo.remainingSeconds, '秒');

                    return true; // 继续检查
                }
            } catch (error) {
                console.error('获取时间状态失败:', error);
            }

            return true; // 出错时继续检查
        }

        function startStatusMonitoring() {
            // 每2秒检查一次状态
            timeCheckInterval = setInterval(async () => {
                if (!timerStarted || verificationCompleted) {
                    return;
                }

                const shouldContinue = await checkTimeStatus();
                if (!shouldContinue) {
                    clearInterval(timeCheckInterval);
                }
            }, 2000);
        }

        async function handleAutoCompletion() {
            if (verificationCompleted) return;

            verificationCompleted = true;

            // 清理定时器
            if (timeCheckInterval) {
                clearInterval(timeCheckInterval);
            }

            // 显示自动完成消息
            showSuccessMessage('系统已自动完成验证');

            setTimeout(() => {
                window.close();
            }, 2000);
        }

        async function manualConfirm() {
            if (verificationCompleted) return;

            // 停止状态检查
            if (timeCheckInterval) {
                clearInterval(timeCheckInterval);
            }

            // 禁用按钮
            const btn = document.getElementById('manualConfirmBtn');
            btn.disabled = true;
            btn.textContent = '🔄 验证中...';

            try {
                const response = await fetch(`/core/user-privilege-verification/{verificationId}/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        content: `QR_VERIFICATION_MANUAL_${Date.now()}`
                    })
                });

                const result = await response.json();

                if (result.success && result.data) {
                    verificationCompleted = true;
                    showSuccessMessage('手动验证成功！');
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                } else {
                    showErrorMessage(result.message || '验证失败，请重试');
                    resetButton();
                }
            } catch (error) {
                console.error('验证请求失败:', error);
                showErrorMessage('网络错误，请重试');
                resetButton();
            }
        }

        function resetButton() {
            const btn = document.getElementById('manualConfirmBtn');
            btn.disabled = false;
            btn.textContent = '✅ 我已完成验证';

            // 重新开始状态监控
            startStatusMonitoring();
        }



        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.innerHTML = `
                <div style="background: #d1fae5; border: 1px solid #10b981; color: #065f46;
                            padding: 20px; border-radius: 12px; text-align: center;
                            font-size: 18px; font-weight: 600; margin: 20px 0;">
                    ✅ ${message}，页面即将关闭...
                </div>
            `;

            document.querySelector('.content').appendChild(successDiv);
        }

        function showErrorMessage(message) {
            alert('❌ ' + message);
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            if (timeCheckInterval) {
                clearInterval(timeCheckInterval);
            }
        });
    </script>
</body>
</html>
