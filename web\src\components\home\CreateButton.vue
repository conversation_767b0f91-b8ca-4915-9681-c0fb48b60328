<template>
  <Add24Regular
    :size="36"
    color="var(--blue)"
    @click="handleClick"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
    @mouseleave="handleMouseUp"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
    class="cursor-pointer create-button"
    ref="createButtonRef"
    :class="{ 'is-rotating': isRotating, 'is-long-pressing': isLongPressing }"
    @mouseenter="handleMouseEnter"
  />
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'

import { Add24Regular } from '@/icons'

const emit = defineEmits(['click', 'long-press'])

// 按钮旋转状态
const isRotating = ref(false)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const isAutoRotating = ref(false)
const rotationTimer = ref<number | null>(null)
const autoRotationTimer = ref<number | null>(null)

// 长按状态
const isLongPressing = ref(false)
const longPressTimer = ref<number | null>(null)
const LONG_PRESS_DURATION = 1000 // 长按时间阈值（毫秒）

// 处理按钮点击
const handleClick = () => {
  // 如果是长按触发的点击，不执行普通点击逻辑
  if (isLongPressing.value) {
    return
  }
  emit('click')
}

// 处理鼠标按下
const handleMouseDown = () => {
  startLongPress()
}

// 处理鼠标抬起
const handleMouseUp = () => {
  stopLongPress()
}

// 处理触摸开始
const handleTouchStart = (event: TouchEvent) => {
  event.preventDefault() // 防止触摸事件触发鼠标事件
  startLongPress()
}

// 处理触摸结束
const handleTouchEnd = (event: TouchEvent) => {
  event.preventDefault()
  stopLongPress()
}

// 开始长按计时
const startLongPress = () => {
  isLongPressing.value = false
  longPressTimer.value = window.setTimeout(() => {
    isLongPressing.value = true
    emit('long-press')
  }, LONG_PRESS_DURATION)
}

// 停止长按计时
const stopLongPress = () => {
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value)
    longPressTimer.value = null
  }
  // 延迟重置长按状态，避免影响点击事件
  setTimeout(() => {
    isLongPressing.value = false
  }, 100)
}

// 处理鼠标悬浮，触发旋转
const handleMouseEnter = () => {
  // 如果已经在旋转中，不需要再次触发
  if (isRotating.value) return

  // 清除任何即将到来的自动旋转
  if (autoRotationTimer.value) {
    clearTimeout(autoRotationTimer.value)
    autoRotationTimer.value = null
  }

  // 设置旋转状态
  isRotating.value = true

  // 设置一个计时器，在旋转动画完成后重置状态
  rotationTimer.value = window.setTimeout(() => {
    isRotating.value = false
    // 重新开始自动旋转计时
    scheduleNextAutoRotation()
  }, 1500) // 动画持续时间
}

// 计划下一次自动旋转
const scheduleNextAutoRotation = () => {
  // 清除任何现有的自动旋转计时器
  if (autoRotationTimer.value) {
    clearTimeout(autoRotationTimer.value)
    autoRotationTimer.value = null
  }

  // 设置一个随机时间（5-15秒）后触发自动旋转
  const nextRotationTime = 5000 + Math.random() * 10000
  autoRotationTimer.value = window.setTimeout(() => {
    triggerAutoRotation()
  }, nextRotationTime)
}

// 触发自动旋转
const triggerAutoRotation = () => {
  // 如果已经在旋转或者用户鼠标悬浮中，不执行自动旋转
  if (isRotating.value) {
    scheduleNextAutoRotation()
    return
  }

  // 设置旋转状态
  isRotating.value = true

  // 旋转完成后重置状态
  rotationTimer.value = window.setTimeout(() => {
    isRotating.value = false
    // 计划下一次自动旋转
    scheduleNextAutoRotation()
  }, 1500) // 动画持续时间
}

onMounted(() => {
  // 开始自动旋转计时
  scheduleNextAutoRotation()
})

onUnmounted(() => {
  // 清除所有计时器
  if (rotationTimer.value) {
    clearTimeout(rotationTimer.value)
    rotationTimer.value = null
  }

  if (autoRotationTimer.value) {
    clearTimeout(autoRotationTimer.value)
    autoRotationTimer.value = null
  }

  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value)
    longPressTimer.value = null
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/ui-elements/long-press';

.create-button {
  transition: all 0.3s ease;

  &.is-long-pressing {
    transform: scale(1.1);
    filter: brightness(1.2);
  }
}
</style>
