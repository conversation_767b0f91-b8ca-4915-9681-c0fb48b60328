<!--
  特权激活码生成表单组件
  
  功能说明：
  - 提供激活码生成表单
  - 支持用户选择、特权类型选择、过期时间设置等
  - 生成激活码后显示并提供复制功能
-->
<template>
  <div class="privilege-code-form">
    <NForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-placement="left"
      label-width="120px"
    >
      <!-- 用户选择 -->
      <NFormItem label="选择用户" path="userId">
        <NSelect
          v-model:value="formData.userId"
          placeholder="搜索并选择用户"
          filterable
          remote
          :options="userOptions"
          :loading="isSearchingUsers"
          @search="handleUserSearch"
          clearable
        />
      </NFormItem>

      <!-- 特权类型 -->
      <NFormItem label="特权类型" path="privilegeType">
        <NRadioGroup v-model:value="formData.privilegeType">
          <NRadio value="template">模板</NRadio>
          <NRadio value="paid">付费</NRadio>
        </NRadioGroup>
      </NFormItem>

      <!-- 模板选择（当选择模板类型时显示） -->
      <NFormItem v-if="formData.privilegeType === 'template'" label="选择模板" path="templateId">
        <NSelect
          v-model:value="formData.templateId"
          placeholder="搜索并选择模板"
          filterable
          remote
          :options="templateOptions"
          :loading="isSearchingTemplates"
          @search="handleTemplateSearch"
          clearable
        />
      </NFormItem>

      <!-- 付费金额（当选择付费类型时显示） -->
      <NFormItem v-if="formData.privilegeType === 'paid'" label="付费金额" path="paidDenomination">
        <NInputNumber
          v-model:value="formData.paidDenomination"
          placeholder="请输入付费金额"
          :min="1"
          :precision="0"
        />
      </NFormItem>

      <!-- 过期时间 -->
      <NFormItem label="过期时间" path="expireTime">
        <NDatePicker
          v-model:value="formData.expireTime"
          type="datetime"
          placeholder="选择过期时间"
          :is-date-disabled="(date: number) => date < Date.now()"
        />
      </NFormItem>

      <!-- 验证类型 -->
      <NFormItem label="验证类型" path="verificationType">
        <NRadioGroup v-model:value="formData.verificationType">
          <NRadio :value="0">短信验证</NRadio>
          <NRadio :value="1">二维码验证</NRadio>
        </NRadioGroup>
      </NFormItem>

      <!-- 提交按钮 -->
      <NFormItem>
        <NButton
          type="primary"
          @click="handleSubmit"
          :loading="isGenerating"
          :disabled="!isFormValid"
        >
          生成CODE
        </NButton>
      </NFormItem>
    </NForm>

    <!-- 生成的激活码显示区域 -->
    <div v-if="generatedCode" class="generated-code-section">
      <NDivider />
      <div class="generated-code-title">生成的激活码：</div>
      <div class="generated-code-display">
        <NInput :value="generatedCode" readonly placeholder="激活码" />
        <NButton type="primary" @click="handleCopyCode" :loading="isCopying"> 复制 </NButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  NForm,
  NFormItem,
  NSelect,
  NRadioGroup,
  NRadio,
  NInputNumber,
  NDatePicker,
  NButton,
  NInput,
  NDivider,
  useMessage,
} from 'naive-ui'
import { ref, computed, reactive } from 'vue'

import privilegeApi, { privilegeTemplateApi } from '@/api/privilege'
import userApi from '@/api/user'

const emit = defineEmits<{
  success: []
}>()

const message = useMessage()

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  userId: null as number | null,
  privilegeType: 'template' as 'template' | 'paid',
  templateId: null as number | null,
  paidDenomination: null as number | null,
  expireTime: null as number | null,
  verificationType: 0 as 0 | 1,
})

// 表单验证规则
const formRules = {
  userId: {
    required: true,
    message: '请选择用户',
    trigger: 'change',
  },
  templateId: {
    required: true,
    message: '请选择模板',
    trigger: 'change',
    validator: () => {
      if (formData.privilegeType === 'template' && !formData.templateId) {
        return false
      }
      return true
    },
  },
  paidDenomination: {
    required: true,
    message: '请输入付费金额',
    trigger: 'change',
    validator: () => {
      if (
        formData.privilegeType === 'paid' &&
        (!formData.paidDenomination || formData.paidDenomination <= 0)
      ) {
        return false
      }
      return true
    },
  },
  expireTime: {
    required: true,
    message: '请选择过期时间',
    trigger: 'change',
  },
}

// 状态
const isGenerating = ref(false)
const isCopying = ref(false)
const isSearchingUsers = ref(false)
const isSearchingTemplates = ref(false)
const generatedCode = ref('')

// 选项数据
const userOptions = ref<Array<{ label: string; value: number }>>([])
const templateOptions = ref<Array<{ label: string; value: number }>>([])

// 表单验证状态
const isFormValid = computed(() => {
  return (
    formData.userId &&
    formData.expireTime &&
    ((formData.privilegeType === 'template' && formData.templateId) ||
      (formData.privilegeType === 'paid' &&
        formData.paidDenomination &&
        formData.paidDenomination > 0))
  )
})

/**
 * 搜索用户
 */
const handleUserSearch = async (query: string) => {
  if (!query.trim()) {
    userOptions.value = []
    return
  }

  isSearchingUsers.value = true
  try {
    const response = await userApi.searchUser(query)
    if (response.success && response.data) {
      userOptions.value = response.data.map((user) => ({
        label: `${user.username} (${user.phone})`,
        value: parseInt(user.id),
      }))
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
    message.error('搜索用户失败')
  } finally {
    isSearchingUsers.value = false
  }
}

/**
 * 搜索模板
 */
const handleTemplateSearch = async (query: string) => {
  if (!query.trim()) {
    templateOptions.value = []
    return
  }

  isSearchingTemplates.value = true
  try {
    const response = await privilegeTemplateApi.search(query)
    if (response.success && response.data) {
      templateOptions.value = response.data.map((template) => ({
        label: `${template.name} (${template.denomination}元)`,
        value: template.id,
      }))
    }
  } catch (error) {
    console.error('搜索模板失败:', error)
    message.error('搜索模板失败')
  } finally {
    isSearchingTemplates.value = false
  }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
  } catch {
    return
  }

  isGenerating.value = true
  try {
    const requestData = {
      userId: formData.userId!,
      expireTime: formData.expireTime!,
      verificationType: formData.verificationType,
      ...(formData.privilegeType === 'template'
        ? { templateId: formData.templateId! }
        : { paidDenomination: formData.paidDenomination! }),
    }

    const response = await privilegeApi.generateCode(requestData)
    if (response.success && response.data) {
      generatedCode.value = response.data
      message.success('激活码生成成功！')
      emit('success')
    } else {
      message.error('生成激活码失败')
    }
  } catch (error) {
    console.error('生成激活码失败:', error)
    message.error('生成激活码失败，请稍后重试')
  } finally {
    isGenerating.value = false
  }
}

/**
 * 复制激活码
 */
const handleCopyCode = async () => {
  if (!generatedCode.value) return

  isCopying.value = true
  try {
    await navigator.clipboard.writeText(generatedCode.value)
    message.success('激活码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  } finally {
    isCopying.value = false
  }
}

/**
 * 重置表单
 */
const reset = () => {
  formData.userId = null
  formData.privilegeType = 'template'
  formData.templateId = null
  formData.paidDenomination = null
  formData.expireTime = null
  formData.verificationType = 0
  generatedCode.value = ''
  userOptions.value = []
  templateOptions.value = []
}

// 暴露方法给父组件
defineExpose({
  reset,
})
</script>

<style lang="scss" scoped>
.privilege-code-form {
  .generated-code-section {
    margin-top: 20px;

    .generated-code-title {
      margin-bottom: 12px;
      font-weight: 500;
      color: var(--text-color-1);
    }

    .generated-code-display {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
}
</style>
